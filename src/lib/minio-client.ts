'use server'

import { Client } from 'minio'
import { v4 as uuidv4 } from 'uuid'

// Configure your MinIO client
const minioClient = new Client({
    endPoint: process.env.NEXT_PUBLIC_MINIO_ENDPOINT || 'localhost',
    port: parseInt(process.env.NEXT_PUBLIC_MINIO_PORT || '9000'),
    useSSL: process.env.NEXT_PUBLIC_MINIO_USE_SSL === 'true',
    accessKey: process.env.NEXT_PUBLIC_MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.NEXT_PUBLIC_MINIO_SECRET_KEY || 'minioadmin',
})

export async function uploadFile(formData: FormData) {
    try {
        const file = formData.get('file') as File

        if (!file) {
            return { success: false, error: 'No file provided' }
        }

        const buffer = Buffer.from(await file.arrayBuffer())
        const fileExtension = file.name.split('.').pop() || ''
        const fileName = `${uuidv4()}.${fileExtension}`
        const bucketName = process.env.NEXT_PUBLIC_MINIO_BUCKET_NAME || 'my-bucket'

        const bucketExists = await minioClient.bucketExists(bucketName)
        if (!bucketExists) {
            await minioClient.makeBucket(bucketName, process.env.NEXT_PUBLIC_MINIO_REGION || 'us-east-1')
        }

        await minioClient.putObject(bucketName, fileName, buffer, buffer.length, {
            'Content-Type': file.type,
        })

        const fileUrl = `${
            process.env.NEXT_PUBLIC_MINIO_PUBLIC_URL ||
            `http://${process.env.NEXT_PUBLIC_MINIO_ENDPOINT}:${process.env.NEXT_PUBLIC_MINIO_PORT}`
        }/${bucketName}/${fileName}`

        return { success: true, fileUrl }
    } catch (error) {
        console.log(error)
        return { success: false, error: 'Upload failed' }
    }
}
