import { useState, useEffect } from 'react'
import type { Message } from '@/types/chat'
import { api } from '@/config/axios-config'

interface ChatMessage {
    sender: 'user' | 'ai'
    content: string
}

export const useChatHistory = (userId: string) => {
    const [sessions, setSessions] = useState<string[]>([])
    const [history, setHistory] = useState<Message[]>([])

    useEffect(() => {
        if (!userId) return

        api.get(`/sessions`, { params: { userId } })
            .then((res) => setSessions(res.data.sessions))
            .catch(console.error)
    }, [userId])

    const loadHistory = async (sessionId: string): Promise<Message[]> => {
        try {
            const res = await api.get(`/sessions/${sessionId}/history`)
            const chatMessages = res.data.history as ChatMessage[]
            const transformedMessages: Message[] = chatMessages.map((msg, index) => ({
                id: `${sessionId}-${index}`,
                content: msg.content,
                sender: msg.sender,
                timestamp: new Date(),
                expanded: false,
            }))
            setHistory(transformedMessages)
            return transformedMessages
        } catch (err) {
            console.error(err)
            return []
        }
    }

    return { sessions, history, loadHistory }
}
