import { create } from 'zustand'
import { Task, Message, ProjectData, Project } from '@/types/project'
import { extractProjectName, extractTechStack } from '@/utils/project.utils'
import { getAllProjects } from '@/services/project.service'

interface ProjectStore {
    // State
    messages: Message[]
    tasks: Task[]
    projectData: ProjectData
    projectSession: string | null
    showTasksPanel: boolean
    isLoading: boolean
    isSaving: boolean
    availableProjects: Project[]
    selectedProject: Project | null
    isLoadingProjects: boolean

    // Actions
    setMessages: (messages: Message[]) => void
    addMessage: (message: Message) => void
    updateMessage: (id: string, contentOrUpdater: string | ((prev: string) => string)) => void
    setTasks: (tasks: Task[]) => void
    addTask: (task: Task) => void
    setProjectSession: (session: string | undefined) => void
    setShowTasksPanel: (show: boolean) => void
    setIsLoading: (loading: boolean) => void
    setIsSaving: (saving: boolean) => void
    updateProjectData: () => void
    resetProject: () => void
    setAvailableProjects: (projects: Project[]) => void
    setSelectedProject: (project: Project | null) => void
    setIsLoadingProjects: (loading: boolean) => void
    fetchAvailableProjects: (workspaceId?: string) => Promise<void>
}

export const useProjectStore = create<ProjectStore>((set) => ({
    // Initial state
    messages: [],
    tasks: [],
    projectData: {
        name: '',
        description: '',
        techStack: {},
        tasks: [],
    },
    projectSession: null,
    showTasksPanel: false,
    isLoading: false,
    isSaving: false,
    availableProjects: [],
    selectedProject: null,
    isLoadingProjects: false,

    // Actions
    setMessages: (messages) => set({ messages }),
    addMessage: (message) =>
        set((state) => ({
            messages: [...state.messages, message],
        })),
    updateMessage: (id, contentOrUpdater) =>
        set((state) => ({
            messages: state.messages.map((msg) =>
                msg.id === id
                    ? {
                          ...msg,
                          content: typeof contentOrUpdater === 'function' ? contentOrUpdater(msg.content) : contentOrUpdater,
                      }
                    : msg,
            ),
        })),
    setTasks: (tasks) => set({ tasks }),
    addTask: (task) =>
        set((state) => ({
            tasks: [...state.tasks, task],
        })),
    setProjectSession: (session) => set({ projectSession: session }),
    setShowTasksPanel: (show) => set({ showTasksPanel: show }),
    setIsLoading: (loading) => set({ isLoading: loading }),
    setIsSaving: (saving) => set({ isSaving: saving }),
    updateProjectData: () =>
        set((state) => {
            const projectName = extractProjectName(state.messages)
            const techStack = extractTechStack(state.messages)
            return {
                projectData: {
                    name: projectName,
                    description: state.projectData.description,
                    techStack,
                    tasks: state.tasks,
                },
            }
        }),
    resetProject: () =>
        set({
            messages: [],
            tasks: [],
            projectData: {
                name: '',
                description: '',
                techStack: {},
                tasks: [],
            },
            projectSession: null,
            showTasksPanel: false,
            isLoading: false,
            isSaving: false,
            selectedProject: null, // Reset selected project
        }),

    setAvailableProjects: (projects) => set({ availableProjects: projects }),
    setSelectedProject: (project) => set({ selectedProject: project }),
    setIsLoadingProjects: (loading) => set({ isLoadingProjects: loading }),

    fetchAvailableProjects: async (workspaceId) => {
        set({ isLoadingProjects: true })
        try {
            const projects = await getAllProjects(workspaceId)
            set({ availableProjects: projects })
        } catch (error) {
            console.error('Error fetching projects:', error)
            set({ availableProjects: [] })
        } finally {
            set({ isLoadingProjects: false })
        }
    },
}))
