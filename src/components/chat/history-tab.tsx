'use client'

import React, { useState, useEffect, FC } from 'react'
import type { Message } from '@/types/chat'
import { api } from '@/config/axios-config'

interface HistoryTabProps {
    userId: string
    onSelectSession: (sessionId: string, messages: Message[]) => void
    fetchSessionMessages: (sessionId: string) => Promise<Message[]>
}

export const HistoryTab: FC<HistoryTabProps> = ({ userId, onSelectSession, fetchSessionMessages }) => {
    const [sessions, setSessions] = useState<string[]>([])
    const [activeSession, setActiveSession] = useState<string | null>(null)
    const [cache, setCache] = useState<Record<string, Message[]>>({})

    useEffect(() => {
        const loadSessions = async () => {
            try {
                const res = await api.get(`/sessions`, { params: { userId } })
                setSessions(res.data.sessions || [])
            } catch (error) {
                console.error('Failed to load sessions:', error)
            }
        }

        loadSessions()
    }, [userId])

    const handleSessionClick = async (sessionId: string) => {
        setActiveSession(sessionId)

        if (cache[sessionId]) {
            onSelectSession(sessionId, cache[sessionId])
        } else {
            const messages = await fetchSessionMessages(sessionId)
            setCache((prev) => ({ ...prev, [sessionId]: messages }))
            onSelectSession(sessionId, messages)
        }
    }

    return (
        <div className="flex h-full w-1/4">
            <nav className="border-r overflow-auto w-full">
                {sessions.map((sid) => (
                    <div
                        key={sid}
                        className={`cursor-pointer p-2 hover:bg-gray-100 ${
                            sid === activeSession ? 'bg-gray-200 font-semibold' : ''
                        }`}
                        onClick={() => handleSessionClick(sid)}>
                        Session {sid.slice(0, 8)}
                    </div>
                ))}
            </nav>
        </div>
    )
}
