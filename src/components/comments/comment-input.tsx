import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Send } from 'lucide-react'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

const CommentInput = ({
    value,
    onChange,
    onSubmit,
    user,
    isLoading,
    placeholder = 'Write a comment...',
    showAvatar = true,
}: {
    value: string
    onChange: (value: string) => void
    onSubmit: () => void
    user?: { id: number; first_name: string; last_name: string; img_url: string | null }
    isLoading: boolean
    placeholder?: string
    showAvatar?: boolean
}) => {
    const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault()
            onSubmit()
        }
    }

    return (
        <div className="flex gap-2 mb-2">
            {showAvatar && (
                <AssigneeAvatar
                    assignee={`${user?.first_name} ${user?.last_name}`}
                    imageUrl={user?.img_url || ''}
                    className="border-2 border-white h-[30px] w-[30px]"
                />
            )}
            <div className="flex-1 flex gap-2 items-center border pr-2 rounded-lg bg-[#F0F2F599]">
                <Textarea
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={placeholder}
                    className="w-full ml-2 border-none shadow-none max-w-[98%] rounded-none px-0 focus-visible:ring-0 focus-visible:border-gray-400 min-h-[61px] max-h-[100px]"
                    onKeyDown={handleKeyPress}
                />
                <Button
                    onClick={onSubmit}
                    size="sm"
                    className="rounded-full w-8 h-8 p-0 bg-gray-900 hover:bg-gray-800"
                    disabled={isLoading}>
                    <Send className="w-4 h-4" />
                </Button>
            </div>
        </div>
    )
}

export default CommentInput
