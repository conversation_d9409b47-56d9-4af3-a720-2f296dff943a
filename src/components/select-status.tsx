'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import getStatusIcon from '@/components/get-status-icon'
import getStatusStyles from '@/utils/get-status-styles.utils'

interface Status {
    id: string | number
    label: string
    color?: string
}

interface StatusSelectProps {
    entityId: string | number
    entityType: 'task' | 'feature' | 'project'
    initialStatusId?: number
    fetchEndpoint: string
    updateEndpoint: string
    className?: string
    onStatusChange?: (newStatus: Status) => void
    disabled?: boolean
    entityDetails?: Record<string, unknown>
    status_key?: string
}

export function StatusSelect({
    entityId,
    entityType,
    initialStatusId,
    fetchEndpoint,
    updateEndpoint,
    className,
    onStatusChange,
    disabled = false,
    entityDetails,
    status_key = 'status',
}: StatusSelectProps) {
    const [selectedStatusId, setSelectedStatusId] = useState<number | undefined>(initialStatusId || undefined)

    // Fetch available statuses
    const { data: statuses, isLoading: isLoadingStatuses } = useQuery({
        queryKey: ['statuses', entityType],
        queryFn: async () => {
            const response = await api.get(fetchEndpoint)
            return response.data.data
        },
    })

    // Update status mutation
    const { mutate: updateStatus, isPending: isUpdating } = useMutation({
        mutationFn: async (statusId: number) => {
            const payload = {
                [`${entityType}_id`]: entityId,
                [status_key]: statusId,
                ...entityDetails,
            }
            const response = await api.put(updateEndpoint, payload)

            return response.data
        },
        onSuccess: (data) => {
            toast.success('Status updated successfully')
            // Find the complete status object to pass to the callback
            if (onStatusChange) {
                onStatusChange(data)
            }
        },
        onError: () => {
            toast.error('Failed to update status')
        },
    })

    // Update selected status when initialStatusId changes
    useEffect(() => {
        if (initialStatusId) {
            setSelectedStatusId(initialStatusId)
        }
    }, [initialStatusId])

    // Handle status selection
    const handleValueChange = (value: number) => {
        updateStatus(value)
    }

    // Get status color based on label

    // Find the selected status object
    const selectedStatus = statuses?.find((status: Status) => status.id === selectedStatusId)

    if (isLoadingStatuses) {
        return (
            <div
                className={cn(
                    'flex h-9 items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm',
                    className,
                )}>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading...</span>
            </div>
        )
    }

    return (
        <Select
            value={selectedStatusId?.toString()}
            onValueChange={(value) => handleValueChange(Number(value))}
            disabled={disabled || isUpdating}>
            <div
                className={cn(
                    'w-full overflow-hidden flex items-center justify-center border',
                    className,
                    getStatusStyles(selectedStatus?.label).border,
                )}>
                <SelectTrigger className="border-none shadow-none gap-0 w-full pl-0">
                    {!isUpdating ? (
                        <SelectValue placeholder="Select status">
                            {selectedStatus && (
                                <div className="flex items-center gap-1">
                                    {getStatusIcon(selectedStatus.label, 12)}
                                    <span
                                        className={cn(
                                            'text-sm',
                                            selectedStatus.color || getStatusStyles(selectedStatus.label).text,
                                        )}>
                                        {selectedStatus.label}
                                    </span>
                                </div>
                            )}
                        </SelectValue>
                    ) : (
                        <div className="w-[80px] flex justify-center">
                            <Loader2 className="h-3 w-3 animate-spin ml-2" />
                        </div>
                    )}
                </SelectTrigger>
            </div>
            <SelectContent>
                {statuses?.map((status: Status) => (
                    <SelectItem key={status.id} value={String(status.id)} className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                            {getStatusIcon(status.label)}
                            <span className={cn('text-xs font-medium', status.color || getStatusStyles(status.label).text)}>
                                {status.label}
                            </span>
                        </div>
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    )
}
