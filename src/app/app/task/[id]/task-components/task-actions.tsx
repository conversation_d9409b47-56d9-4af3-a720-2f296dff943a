'use client' // If using app directory

import { useState } from 'react'
import { Pencil, Share2, Trash2 } from 'lucide-react' // Optional icon
import { UpdateTaskModal } from './update-task'
import { DeleteWithAlert } from '@/components/delete-with-alert-dialog'
import { TaskFormValues } from '@/app/app/planner/create-task/create-task-form'
import endpoints from '@/services/api-endpoints'
import { TaskData } from './task-details-card'
import { Button } from '@/components/ui/button'

interface TaskActionsProps {
    updateDefaultValues: TaskFormValues & { taskId: number; shortCode: string }
    refetchTask: () => void
    taskData: TaskData
    onAftedDeleteSuccess: () => void
}

export default function TaskActions({ updateDefaultValues, refetchTask, taskData, onAftedDeleteSuccess }: TaskActionsProps) {
    const [editOpen, setEditOpen] = useState(false)
    const [deleteOpen, setDeleteOpen] = useState(false)

    const handleEdit = () => {
        setEditOpen(true)
    }

    const handleDelete = () => {
        setDeleteOpen(true)
    }

    return (
        <>
            <div className="flex max-h-8">
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleEdit}>
                    <Pencil className="h-4 w-4" color="#5B6871" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => console.log('share')}>
                    <Share2 className="h-4 w-4" color="#5B6871" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={handleDelete}>
                    <Trash2 className="h-4 w-4" color="#5B6871" />
                </Button>
            </div>

            <UpdateTaskModal
                defaultValues={updateDefaultValues}
                onAfterSuccess={refetchTask}
                isUpdateOpen={editOpen}
                setIsUpdateOpen={setEditOpen}
            />

            <DeleteWithAlert
                title="Are you sure you want to delete this task?"
                description="This action cannot be undone."
                endpoint={`${endpoints.tasks.deleteTask}/${taskData.id}`}
                onAfterSuccess={onAftedDeleteSuccess}
                isAlertOpen={deleteOpen}
                setIsAlertOpen={setDeleteOpen}
            />
        </>
    )
}
