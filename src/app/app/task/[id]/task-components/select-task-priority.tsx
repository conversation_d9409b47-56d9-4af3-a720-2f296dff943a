'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Flame, Loader2 } from 'lucide-react'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Priority {
    id: number
    label: string
    level: number
}

interface PrioritySelectProps {
    initialPriorityId?: number
    fetchEndpoint: string
    updateEndpoint: string
    className?: string
    onPriorityChange?: (newPriority: Priority) => void
    disabled?: boolean
    entityDetails?: Record<string, unknown>
}

export function PrioritySelect({
    initialPriorityId,
    fetchEndpoint,
    updateEndpoint,
    className,
    onPriorityChange,
    disabled = false,
    entityDetails,
}: PrioritySelectProps) {
    const [selectedPriorityId, setSelectedPriorityId] = useState<number | undefined>(initialPriorityId)

    const { data: priorities, isLoading: isLoadingPriorities } = useQuery({
        queryKey: ['priorities'],
        queryFn: async () => {
            const response = await api.get(fetchEndpoint)
            return response.data.data as Priority[]
        },
    })

    const { mutate: updatePriority, isPending: isUpdating } = useMutation({
        mutationFn: async (priorityId: number) => {
            const payload = {
                priority_id: priorityId,
                ...entityDetails,
            }
            const response = await api.put(updateEndpoint, payload)
            return response.data
        },
        onSuccess: (data) => {
            const priorityData = data?.data
            setSelectedPriorityId(priorityData?.priority_id)
            toast.success('Priority updated successfully')
            if (onPriorityChange && priorityData) {
                onPriorityChange(priorityData)
            }
        },
        onError: () => {
            toast.error('Failed to update priority')
        },
    })

    useEffect(() => {
        if (initialPriorityId) {
            setSelectedPriorityId(initialPriorityId)
        }
    }, [initialPriorityId])

    const handleValueChange = (value: number) => {
        updatePriority(value)
    }

    const selectedPriority = priorities?.find((p) => p.id === selectedPriorityId)

    if (isLoadingPriorities) {
        return (
            <div
                className={cn(
                    'flex h-9 items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm',
                    className,
                )}>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading...</span>
            </div>
        )
    }

    return (
        <Select
            value={selectedPriorityId?.toString()}
            onValueChange={(value) => handleValueChange(Number(value))}
            disabled={disabled || isUpdating}>
            <div className={cn('w-full overflow-hidden flex items-center justify-center', className)}>
                <SelectTrigger className="border-none shadow-none gap-0 w-full pl-0">
                    {!isUpdating ? (
                        <SelectValue placeholder="Select priority">
                            {selectedPriority && (
                                <div className="text-xs">
                                    {selectedPriority.label === 'High' ? (
                                        <div className="flex items-center">
                                            <Flame className="mr-1 h-4 w-3 " color="#E35422" />
                                            <span className="text-[#E35422]">{selectedPriority.label}</span>
                                        </div>
                                    ) : (
                                        selectedPriority.label
                                    )}
                                </div>
                            )}
                        </SelectValue>
                    ) : (
                        <div className="w-[80px] flex justify-center">
                            <Loader2 className="h-3 w-3 animate-spin ml-2" />
                        </div>
                    )}
                </SelectTrigger>
            </div>
            <SelectContent>
                {priorities?.map((priority: Priority) => (
                    <SelectItem key={priority.id} value={String(priority.id)}>
                        <div className="flex items-center gap-2 text-sm">{priority.label}</div>
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>
    )
}
