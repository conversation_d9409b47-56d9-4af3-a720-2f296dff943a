import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from '@/components/ui/accordion'
import DependentTaskIcon from '../../../../../../public/assets/icons/dependent-task-icon.png'
import Image from 'next/image'
import DepartmentChip from '@/components/ui/department-chip'
import getStatusIcon from '@/components/get-status-icon'
import getStatusStyles from '@/utils/get-status-styles.utils'
import { cn } from '@/lib/utils'
import { Link2 } from 'lucide-react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'

type TaskDepartmentType = {
    id: number
    short_code: string
    label: string
}
type DependentTaskType = {
    id: number
    short_code: string
    title: string
    status_id: number
    taskStatus: {
        id: number
        short_code: string
        label: string
    }
    task_dependency_mapping: {
        dependency_level: number
    }
    taskDepartment?: TaskDepartmentType
    taskAssignees?:
        | [
              {
                  id: number
                  first_name: string
                  last_name: string
                  img_url?: string
              },
          ]
        | []
}

type DependentTasks = DependentTaskType[]

export default function DependentTaskAccordian({ dependentTasks }: { dependentTasks: DependentTasks }) {
    // const [isOpen, setIsOpen] = useState(false)
    const formatTaskShortCodes = (tasks: DependentTasks) => {
        const codes = tasks.map((task) => task.short_code)

        if (codes.length === 0) return ''
        if (codes.length === 1) return codes[0]
        if (codes.length === 2) return `${codes[0]} and ${codes[1]}`

        const allButLast = codes.slice(0, -1).join(', ')
        const last = codes[codes.length - 1]
        return `${allButLast} and ${last}`
    }
    return (
        <Accordion type="single" collapsible className="w-full border-none rounded-[10px] relative">
            <AccordionItem value="item-1" className="space-y-2">
                <AccordionTrigger className="hover:no-underline cursor-pointer border p-3 max-w-fit [&>svg]:bg-[#5B687114] [&>svg]:rounded-full  [&>svg]:p-1  [&>svg]:h-[25px] [&>svg]:w-[25px]">
                    <div className="flex items-center gap-2">
                        <div>
                            <Image src={DependentTaskIcon} alt="Dependent Task Icon" width={32} className="h-auto" />
                        </div>
                        <div className="flex flex-col">
                            <p className="text-[12px] text-[#5C5F62]">{formatTaskShortCodes(dependentTasks)}</p>

                            <p className="text-xs text-[#1C1C1C66]">Dependent Tasks</p>
                        </div>
                    </div>
                </AccordionTrigger>
                <AccordionContent>
                    <div className="border bg-[#FCFCFC] rounded-md py-4 shadow-sm min-w-5xl absolute">
                        <div className="flex mb-2 items-center gap-2 px-4">
                            <div className="border rounded-sm p-1 bg-[#F8F8F8]">
                                <Link2 size={13} color="#9B9B9B" />
                            </div>
                            <p className="text-sm text-[#09090B] font-medium">
                                Depenedent tasks Linked ({dependentTasks.length})
                            </p>
                        </div>
                        <ScrollArea className="h-55" type="scroll">
                            {dependentTasks.map((task) => (
                                <AccordionContent
                                    // className={index === 0 ? 'mx-2 p-4 border-b border-[#EAEAEA]' : 'mx-2 p-4 border-t border-[#EAEAEA]'}
                                    className="mx-2 mb-2 p-4 border border-[#D8D8D896] bg-[#F2F2F282] rounded-[7px]"
                                    key={task.id}>
                                    <DependentTaskItem task={task} />
                                </AccordionContent>
                            ))}
                            {dependentTasks.length === 1 && (
                                <div className="pt-8 italic w-full text-center">No more Dependent tasks</div>
                            )}
                        </ScrollArea>
                    </div>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    )
}

const DependentTaskItem = ({ task }: { task: DependentTaskType }) => {
    return (
        <div>
            <div className="space-y-2 min-w-70">
                {task?.taskDepartment?.label && (
                    <DepartmentChip shortCode={task?.taskDepartment?.short_code} label={task?.short_code} size="sm" />
                )}
                <p className="text-sm text-[#000] font-medium">{task.title}</p>
            </div>
            <div className="w-full min-w-70">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-xs text-[#3C557A] w-[20%]">Assignee</p>
                    </div>
                    <div className="flex w-[80%]">
                        {task?.taskAssignees?.length === 0 && <p className="text-xs text-[#3C557A]">No Assignees</p>}
                        {task?.taskAssignees?.map((assignee) => (
                            <div key={assignee.id} className="flex items-center gap-2 text-xs text-[#3C557A]">
                                <p>{assignee.first_name + ' ' + assignee.last_name}</p>
                                <AssigneeAvatar
                                    assignee={assignee.first_name + ' ' + assignee.last_name}
                                    imageUrl={assignee.img_url}
                                    className="h-[28px] w-[28px]"
                                />
                            </div>
                        ))}
                    </div>
                </div>
                <div className="flex items-center justify-between w-full">
                    <div className=" w-[20%]">
                        <p className="text-xs text-[#3C557A]">Status</p>
                    </div>

                    <div className="gap-1 flex items-center w-[80%]">
                        <div>{getStatusIcon(task.taskStatus.label)}</div>
                        <p className={cn('text-xs font-medium w-fit  rounded-md', getStatusStyles(task.taskStatus.label).text)}>
                            {task.taskStatus.label}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
}
