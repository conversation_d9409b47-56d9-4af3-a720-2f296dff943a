'use client'

import { useState, useRef, useEffect } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from 'date-fns'
import { CalendarIcon, CircleCheckBig, FolderOpenDot, Info, Paperclip, Puzzle, UserRoundPlus, X } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { PaginatedSelect } from '@/components/paginated-select'
import endpoints from '@/services/api-endpoints'
import { PaginatedComboBox, PaginatedComboBoxRef } from '@/components/popover-comand-list'
import { useAuthStore } from '@/store/auth.store'
import { Tooltip, TooltipContent } from '@/components/ui/tooltip'
import { TooltipTrigger } from '@radix-ui/react-tooltip'
import { AssigneeAvatar } from '@/components/assignee-avatar-with-fallback'
import { JoinTwoStrings } from '@/utils/join-two-strings'

type UserType = { id: number; first_name: string; last_name: string; img_url: string }
// Define the schema for form validation using Zod
export const taskFormSchema = z.object({
    taskName: z.string().min(3, {
        message: 'Task name must be at least 3 characters.',
    }),
    project: z.string().min(1, {
        message: 'Project is required.',
    }),
    feature: z.string().min(1, {
        message: 'Feature is required.',
    }),
    teamMembers: z.array(z.number()).optional(),
    department: z.string().min(1, {
        message: 'Department is required.',
    }),
    status: z.string().min(1, {
        message: 'Status is required.',
    }),
    priority: z.string().min(1, {
        message: 'Priority is required.',
    }),
    dueDate: z.date().min(new Date(new Date().setHours(0, 0, 0, 0)), {
        message: 'Due date must be today or in the future',
    }),
    parentTaskId: z.string().optional(),
    description: z.string().optional(),
    isBug: z.boolean(),
})

export type TaskFormValues = z.infer<typeof taskFormSchema>
type DefaultValuesType = {
    taskName: string
    project: string
    feature: string
    teamMembers: number[]
    department: string
    status: string
    priority: string
    dueDate: Date
    parentTaskId: string
    description: string
    isBug: boolean
    projectName: string
    featureName: string
    assignees: UserType[]
    departmentName: string
    statusName: string
    priorityName: string
}

interface CreateTaskFormProps {
    onSubmit: (data: TaskFormValues) => void
    defaultValues?: Partial<DefaultValuesType> & { taskassigneesArray?: UserType[] }
    isUpdate?: boolean
}

export function CreateTaskForm({ onSubmit, defaultValues, isUpdate = false }: CreateTaskFormProps) {
    const [selectedMembers, setSelectedMembers] = useState<object[]>([])
    const currentWorkspace = useAuthStore((state) => state.currentWorkspace)
    const [selectedProject, setSelectedProject] = useState<Array<string>>([])
    const comboBoxRef = useRef<PaginatedComboBoxRef>(null)
    // Initialize the form with default values
    const form = useForm<TaskFormValues>({
        resolver: zodResolver(taskFormSchema),
        defaultValues: {
            taskName: '',
            project: '',
            feature: '',
            teamMembers: [],
            department: '',
            status: '', // Default to "Back Log"
            priority: '',
            dueDate: undefined,
            parentTaskId: '',
            description: '',
            isBug: false,
            ...defaultValues,
        },
    })

    // Handle form submission
    function handleSubmit(data: TaskFormValues) {
        onSubmit(data)
    }

    // Handle adding a team member
    const addTeamMember = (members: Record<string, unknown>[]) => {
        const newMembers = members.map((member: unknown) => (member as { id: number }).id)
        setSelectedMembers(members)
        form.setValue('teamMembers', newMembers)
    }

    const removeTeamMember = (member: UserType) => {
        const updatedMembers = selectedMembers.filter((m) => (m as UserType).id !== member.id)
        setSelectedMembers(updatedMembers)
        form.setValue(
            'teamMembers',
            updatedMembers.map((m) => (m as UserType).id),
        )
        // If the combobox is rendered, also update its internal state
        if (comboBoxRef.current) {
            comboBoxRef.current.removeItem(member)
        }
    }

    useEffect(() => {
        if (defaultValues?.taskassigneesArray?.length) {
            setSelectedMembers(defaultValues.taskassigneesArray)
            form.setValue(
                'teamMembers',
                defaultValues.taskassigneesArray.map((member: UserType) => member.id),
            )
        }
    }, [defaultValues?.taskassigneesArray, form])
    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-2">
                {/* Task Name */}
                <FormField
                    control={form.control}
                    name="taskName"
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <div className="relative flex items-center">
                                    <div className="mr-2 rounded-md border p-2">
                                        <CircleCheckBig className="h-5 w-5 text-teal-600" />
                                    </div>
                                    <Input
                                        {...field}
                                        className="border-0 p-0 shadow-none focus-visible:ring-0 placeholder-transparent font-medium md:text-[18px] text-[#191919DE]"
                                    />
                                    {!field.value && (
                                        <div className="absolute left-12 top-0 h-full flex items-center pr-2 font-medium text-[18px] text-[#605c5cde] pointer-events-none">
                                            Enter Task Name
                                        </div>
                                    )}
                                </div>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Project Selection */}
                <FormField
                    control={form.control}
                    name="project"
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <div className="flex items-center" style={{ color: field.value ? '#08B38B' : '#373737' }}>
                                    <FolderOpenDot
                                        className="h-4 w-4 text-muted-foreground"
                                        color={field.value ? '#08B38B' : '#373737'}
                                    />
                                    <PaginatedSelect
                                        endpoint={endpoints.meta.getProjects}
                                        onSelectionChange={(value) => {
                                            const selectedProjects = value.map((project) => String(project.id))
                                            setSelectedProject(selectedProjects || [])
                                            field.onChange(String(value[0]?.id))
                                        }}
                                        params={{ workspace_id: currentWorkspace?.id }}
                                        placeholder="Choose Project"
                                        returnKeys={['id']}
                                        triggerClassName="border-none shadow-none focus:ring-0"
                                        searchParam="searchQuery"
                                        defaultValueLabel={defaultValues?.projectName}
                                    />
                                </div>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Feature Selection */}
                <FormField
                    control={form.control}
                    name="feature"
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <div className="flex items-center">
                                    <Puzzle className="h-4 w-4 text-muted-foreground" />
                                    <PaginatedSelect
                                        endpoint={endpoints.meta.getFeatures}
                                        params={{ project_id: [form.watch('project')], workspace_id: currentWorkspace?.id }}
                                        onSelectionChange={(value) => {
                                            field.onChange(String(value[0]?.id))
                                        }}
                                        placeholder="Select Feature"
                                        returnKeys={['id']}
                                        triggerClassName="border-none shadow-none focus:ring-0"
                                        disabled={!form.watch('project')}
                                        labelKey="title"
                                        defaultValueLabel={defaultValues?.featureName}
                                    />
                                </div>
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Team Members */}
                <div className="w-full border-b">
                    <div className="flex gap-2 items-center">
                        <UserRoundPlus className="h-4 w-4 text-muted-foreground" />
                        <p className="text-sm text-[#373737] font-medium">Assign Team Members</p>
                    </div>
                    <div className="flex w-full items-start gap-2 relative">
                        {/* This will grow as more members are added */}
                        <div className="flex  mt-2 gap-2 absolute z-10">
                            {(selectedMembers as UserType[]).map((member) => {
                                const fullName = JoinTwoStrings(member?.first_name, member?.last_name, ' ')
                                return (
                                    <div
                                        key={member.id}
                                        className="text-sm gap-2 w-full max-w-[171px] h-[25px] flex items-center rounded-full bg-[#F3F3F3] py-0 px-1">
                                        <AssigneeAvatar
                                            imageUrl={member?.img_url}
                                            assignee={fullName}
                                            className="border border-white h-[20px] w-[20px]"
                                        />
                                        <p className="text-[12px] overflow-hidden overflow-ellipsis whitespace-nowrap">
                                            {fullName}
                                        </p>
                                        <X size={16} className="cursor-pointer" onClick={() => removeTeamMember(member)} />
                                    </div>
                                )
                            })}
                        </div>

                        {/* This will shrink if the first one grows */}
                        <div className="flex w-full z-0">
                            <PaginatedComboBox
                                ref={comboBoxRef}
                                endpoint={endpoints.meta.getAssignees}
                                params={{ project_id: selectedProject, workspace_id: currentWorkspace?.id }}
                                onSelectionChange={(value) => {
                                    addTeamMember(value)
                                }}
                                placeholder=""
                                returnKeys={['id', 'first_name', 'last_name', 'img_url']}
                                triggerClassName="border-none hover:bg-transparent shadow-none focus:ring-0 bg-transparent justify-end "
                                labelKey="first_name"
                                multiple
                                initialSelected={defaultValues?.taskassigneesArray}
                            />
                        </div>
                    </div>

                    {form.formState.errors.teamMembers && (
                        <p className="text-sm font-medium text-destructive mt-1">{form.formState.errors.teamMembers.message}</p>
                    )}
                </div>
                {/* Department, Status, Priority */}
                <div className="grid grid-cols-3 gap-4">
                    {/* Department */}
                    <FormField
                        control={form.control}
                        name="department"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel className="text-[#414651] text-[13px] font-medium">Departments</FormLabel>
                                <PaginatedSelect
                                    endpoint={endpoints.meta.getDepartments}
                                    onSelectionChange={(value) => {
                                        field.onChange(String(value[0]?.id))
                                    }}
                                    placeholder="Select Department"
                                    returnKeys={['id']}
                                    labelKey="label"
                                    enableSearch={false}
                                    defaultValueLabel={defaultValues?.departmentName}
                                />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Status */}
                    <FormField
                        control={form.control}
                        name="status"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel className="text-[#414651] text-[13px] font-medium">Status</FormLabel>
                                <PaginatedSelect
                                    endpoint={endpoints.meta.getStatuses}
                                    onSelectionChange={(value) => {
                                        field.onChange(String(value[0]?.id))
                                    }}
                                    placeholder="Select Status"
                                    returnKeys={['id']}
                                    labelKey="label"
                                    enableSearch={false}
                                    defaultValueLabel={defaultValues ? defaultValues?.statusName : 'Backlog'}
                                />
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Priority */}
                    <FormField
                        control={form.control}
                        name="priority"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel className="text-[#414651] text-[13px] font-medium">Priority</FormLabel>
                                <PaginatedSelect
                                    endpoint={endpoints.meta.getPriorities}
                                    onSelectionChange={(value) => {
                                        field.onChange(String(value[0]?.id))
                                    }}
                                    placeholder="Select Priority"
                                    returnKeys={['id']}
                                    labelKey="label"
                                    enableSearch={false}
                                    defaultValueLabel={defaultValues ? defaultValues?.priorityName : 'Medium'}
                                />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Due Date and Parent Task */}
                <div className="grid grid-cols-3 gap-4 justify-center">
                    {/* Due Date */}
                    <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                            <FormItem className="flex flex-col">
                                <FormLabel className="text-[#414651] text-[13px] font-medium">Due Date</FormLabel>
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <FormControl>
                                            <Button
                                                variant={'outline'}
                                                className={cn(
                                                    'pl-3 text-left font-normal',
                                                    !field.value && 'text-muted-foreground',
                                                )}>
                                                {field.value ? format(field.value, 'PPP') : <span>Pick Deadline</span>}
                                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                            </Button>
                                        </FormControl>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                                    </PopoverContent>
                                </Popover>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    {/* Parent Task ID */}
                    <FormField
                        control={form.control}
                        name="parentTaskId"
                        render={({ field }) => (
                            <FormItem>
                                <div>
                                    <FormLabel className="text-[#414651] text-[13px] font-medium mb-2">
                                        Link to Parent Task
                                    </FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Input placeholder="Enter Task ID" {...field} />
                                            <Tooltip>
                                                <TooltipTrigger className="absolute right-2 top-0 h-full">
                                                    <Info className="h-4 w-4" />
                                                </TooltipTrigger>
                                                <TooltipContent
                                                    side="top"
                                                    sideOffset={5}
                                                    className="bg-white text-sm text-[#373737] rounded-md border shadow-lg max-w-[237px]"
                                                    hideArrow>
                                                    <div className=" flex flex-col gap-2 text-[#09090B]">
                                                        <div className="flex gap-2 items-center font-semibold">
                                                            <Paperclip className="h-4 w-4" />
                                                            <p>Link to Parent Task</p>
                                                        </div>
                                                        <div className="w-full leading-[20px]">
                                                            Enter the Task ID of an existing task to map this one as a child.
                                                        </div>
                                                    </div>
                                                </TooltipContent>
                                            </Tooltip>
                                        </div>
                                    </FormControl>
                                </div>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Description */}
                <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="text-[#414651] text-[13px] font-medium">Description</FormLabel>
                            <FormControl>
                                <Textarea placeholder="Enter Description" className="min-h-[80px] max-h-[150px]" {...field} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                {/* Mark as Bug */}
                <div className="my-4">
                    <FormField
                        control={form.control}
                        name="isBug"
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-start space-x-3 space-y-0 rounded-md">
                                <FormControl>
                                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                                </FormControl>
                                <FormLabel className="font-normal">Mark as Bug</FormLabel>
                            </FormItem>
                        )}
                    />
                </div>
                <Button type="submit" className="w-full">
                    {isUpdate ? 'Update Task' : 'Add Task'}
                </Button>
            </form>
        </Form>
    )
}
