'use client'

import { useEffect } from 'react'

import { useBreadcrumbStore } from '@/store/breadcrumb.store'
import { ProjectProgressDetails } from './project-details'
import ProjectTabs from './bottom-tab-section'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/config/axios-config'
import { toast } from 'sonner'
import { useParams } from 'next/navigation'
import endpoints from '@/services/api-endpoints'
import { Bug, CircleCheck, Loader, OctagonAlert, Puzzle } from 'lucide-react'
import TaskDistributionGraph from './task-distribution-graph'
import Comments from '@/components/comments'
import { DepartmentType } from '@/components/project-card'

export type ProjectType = {
    id: number
    name: string
    overview: string
    workspace_id: number
    status_id: number
    stack: Array<string>
    created_at: string
    updatedAt: string
    deletedAt: null
    created_by: number
    creator: {
        id: number
        first_name: string
        last_name: string
        img_url?: string
    }
    status: {
        id: number
        short_code: string
        label: string
        colour: string
    }
    features: {
        completed: number
        total: number
    }
    departments: DepartmentType[]
    team: Array<{
        id: number
        first_name: string
        last_name: string
        avatar?: string
    }>
    estimated_effort: string
    timeline: {
        start_date: string
        end_date: string
        days: number
    }
    priority: {
        level: string
        color: string
    }
    progress: {
        percentage: number
        completed: number
        total: number
    }
    task_stats: {
        completed_tasks: number
        delayed_tasks: number
        open_bugs: number
    }
}

export default function Page() {
    const { setBreadcrumbs } = useBreadcrumbStore()

    const params = useParams()
    const projectId = params.id

    // Fetch project data
    const {
        data: project,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ['project', projectId],
        queryFn: async () => {
            try {
                const response = await api.get(`${endpoints.project.listProjects}/${projectId}`)
                return response.data.data
            } catch (error) {
                toast.error('Failed to fetch project details')
                throw error
            }
        },
        enabled: !!projectId,
        staleTime: 1 * 60 * 1000, // 1 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
    })

    useEffect(() => {
        setBreadcrumbs([
            { label: 'Dashboard', href: '/app' },
            { label: 'Projects', href: '/app/projects' },
            { label: project?.name || 'Project Name', href: `/app/projects/${projectId}` },
        ])
    }, [setBreadcrumbs, project?.name, projectId])

    if (isLoading) {
        return (
            <div className="w-full flex items-center min-h-screen justify-center shadow-none border-none bg-transparent pt-0">
                <Loader className="animate-spin" />
            </div>
        )
    }

    return (
        <>
            <div className="flex w-full gap-2 mb-2">
                <div className="w-[70%] space-y-4 border-b mr-4">
                    <div>
                        <ProjectProgressDetails project={project} />
                        <ProjectTabs project={project} refetchProject={refetch} />
                    </div>
                </div>
                <div>
                    <ProjectCounts project={project} />
                    <div className="border rounded-[8px]">
                        <TaskDistributionGraph />
                    </div>
                </div>
            </div>
            <Comments entity_id={projectId as string} entity_type="project" />
        </>
    )
}

const ProjectCounts = ({ project }: { project: ProjectType }) => {
    const commonCardStyles = 'border rounded-[8px] p-4 shadow-xs font-medium'
    const countStyle = 'text-sm mt-4'

    return (
        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#7E7E80]">
                    <Puzzle size={14} />
                    <p>Features</p>
                </div>
                <p className={countStyle}>{project?.features?.total}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#28A745]">
                    <CircleCheck size={14} />
                    <p>Completed Tasks</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.completed_tasks}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#EC6A5B]">
                    <OctagonAlert size={14} />
                    <p>Delayed Tasks</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.delayed_tasks}</p>
            </div>
            <div className={commonCardStyles}>
                <div className="flex items-center gap-2 text-[#FF6D00]">
                    <Bug size={14} />
                    <p>Open Bugs</p>
                </div>
                <p className={countStyle}>{project?.task_stats?.open_bugs}</p>
            </div>
        </div>
    )
}
