import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
    reactStrictMode: false,
    devIndicators: false,
    experimental: {
        serverActions: {
            bodySizeLimit: '10mb',
        },
    },
    images: {
        remotePatterns: [
            {
                protocol: 'http',
                hostname: '**************',
                port: '9000',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'minio.raydian.ai',
                pathname: '/**',
            },
        ],
    },
}

export default nextConfig
